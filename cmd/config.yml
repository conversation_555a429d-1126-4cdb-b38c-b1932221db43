# server
rpc_server_name: login
rpc_port: 11301

# 端口号
http_port: 21301

# 日志相关
log_level: trace
log_write: true
log_dir: ../../logs/login
log_json: false
log_kafka_enable: true

redis_addr: ************:6379
redis_passwd: 8888

redis_list:
  gateway:
    addr: ************:6379
    passwd: 8888

  player:
    addr: ************:6379
    passwd: 8888

consul_addr: ************:8500

nsqd_addr: ************:4150
nsqd_http_addr: ************:4151
nsqlookupd_addrs:
  - ************:4161

# 消息路由表,在router.yml文件配置
message_router:
  gate: [1000, 1099]
  login: [1100, 1199]

auth_key: keepfancy

rpc_server_tags: normal

# 地理位置数据库路径
geo_db_path: ../GeoLite2-City.mmdb

kafka-producer:
  brokers: ["************:9092"]
  timeout: 10

# SDK 凭证配置
sdk_credentials:
  1001:
    apple:
      pkg_name: "com.playsparkle.apps.mt.ios"
      client_id: "1626967967"
      key_id: "6U85ZUQ8TD"
      team_id: "X2T26K95ZU"
*************************************************************************************************************************************************************************************************************************************************************************************************************************
    facebook:
      client_secret: "f3205917fb373d49810673b218e4d859"
    google:
      client_id: "1042706838390-2tdjhdhrcall2rieio1nchfi1esg4epg.apps.googleusercontent.com"
      client_secret: "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"

  1002:
    facebook:
      client_secret: "f3205917fb373d49810673b218e4d859"
    google:
      client_id: "1042706838390-2tdjhdhrcall2rieio1nchfi1esg4epg.apps.googleusercontent.com"
      client_secret: "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"

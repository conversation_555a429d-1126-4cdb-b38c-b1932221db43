package main

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"github.com/go-redis/redis/v8"
)

// --- 常量定义 ---
const (
	sequenceBits uint  = 56                        // 序列号位数，32位
	maskBits     uint  = 8                         // 随机掩码位数
	maxSequence  int64 = -1 ^ (-1 << sequenceBits) // 最大序列号
	maxMask      int64 = -1 ^ (-1 << maskBits)     // 最大掩码值
)

// IDGenerator 定义一个短ID生成器
type IDGenerator struct {
	redisClient *redis.Client
	keyTTL      time.Duration // Redis Key 的过期时间
}

// NewIDGenerator 创建一个新的短ID生成器实例
func NewIDGenerator(client *redis.Client, keyTTL time.Duration) *IDGenerator {

	return &IDGenerator{
		redisClient: client,
		keyTTL:      keyTTL,
	}
}

// getLastSequenceFromDB 是一个占位函数，您需要根据自己的业务实现它。
// 它的作用是：当Redis中没有序列号时，从数据库中恢复。
func (g *IDGenerator) getLastSequenceFromDB(ctx context.Context) (int64, error) {
	// --- 您需要在此处实现数据库查询逻辑 ---
	// 伪代码:
	// maxID, err := YourDB.Query("SELECT MAX(id) FROM users WHERE ...")
	// if err != nil { return 0, err }
	// if maxID == 0 { return 0, nil } // 如果数据库为空，从0开始
	// parsedInfo := ParseID(maxID)
	// return parsedInfo.Sequence, nil
	// ------------------------------------

	// 作为演示，我们假设数据库为空，总是从0开始
	return 0, nil
}

// NextID 生成下一个唯一ID，包含恢复和过期逻辑
func (g *IDGenerator) NextID(ctx context.Context) (int64, error) {
	// 1. 构造Redis Key
	redisKey := "idgen:seq"

	// 2. 检查Key是否存在
	exists, err := g.redisClient.Exists(ctx, redisKey).Result()
	if err != nil {
		return 0, fmt.Errorf("redis EXISTS failed: %w", err)
	}

	// 3. 如果Key不存在，从数据库恢复并初始化
	if exists == 0 {
		// 从数据库获取该业务最后的序列号
		lastSequence, err := g.getLastSequenceFromDB(ctx)
		if err != nil {
			return 0, fmt.Errorf("failed to get last sequence from DB: %w", err)
		}

		// 使用SETNX原子地设置初始值。
		// 如果在检查和设置之间有其他请求已创建了Key，此操作会失败，从而保证只有一个请求能成功初始化。
		// 这是一种无需显式分布式锁的并发安全方法。
		g.redisClient.SetNX(ctx, redisKey, lastSequence, g.keyTTL).Result()
	}

	// 4. 使用Redis Pipeline执行原子性的INCR和EXPIRE
	var sequenceCmd *redis.IntCmd
	pipe := g.redisClient.TxPipeline()
	sequenceCmd = pipe.Incr(ctx, redisKey)
	pipe.Expire(ctx, redisKey, g.keyTTL)

	_, err = pipe.Exec(ctx)
	if err != nil {
		// 可以加入重试逻辑
		return 0, fmt.Errorf("redis transaction for INCR/EXPIRE failed: %w", err)
	}

	sequence, err := sequenceCmd.Result()
	if err != nil {
		return 0, fmt.Errorf("failed to get INCR result from pipeline: %w", err)
	}

	// 5. 检查序列号是否溢出
	if sequence > maxSequence {
		return 0, fmt.Errorf("sequence exhausted for")
	}

	// 6. 生成8位随机掩码
	rand.Seed(time.Now().UnixNano())
	mask := rand.Int63n(maxMask + 1)

	// 7. 组合所有部分生成最终ID
	// 使用高32位存储序列号，低8位存储随机掩码
	id := (sequence << maskBits) | mask

	return id, nil
}

func main() {
	fmt.Println("ID Generator with 32-bit sequence number and 8-bit random mask")
	fmt.Println("================================================================")

	// 创建一个模拟的Redis客户端（在实际使用中应该连接到真实的 Redis 服务器）
	redisClient := redis.NewClient(&redis.Options{
		Addr:     "************:6379",
		Password: "8888",
	})

	// 创建ID生成器实例
	idGen := NewIDGenerator(redisClient, time.Hour)

	// 创建上下文
	ctx := context.Background()

	// 生成几个ID进行测试
	fmt.Println("\n生成ID测试:")
	ids := make([]int64, 1000)
	for i := 0; i < 1000; i++ {
		id, err := idGen.NextID(ctx)
		if err != nil {
			fmt.Printf("生成ID失败: %v\n", err)
			continue
		}
		ids[i] = id
		fmt.Printf("生成的ID: %d\n", id)
	}

	// 解析ID测试
	fmt.Println("\n解析ID测试:")
	for i, id := range ids {
		parsed := ParseID(id)
		fmt.Printf("ID %d: %d -> 序列号: %d, 掩码: %d\n", i+1, id, parsed.Sequence, parsed.Mask)
	}

	// 验证解析的正确性
	fmt.Println("\n验证解析正确性:")
	for i, id := range ids {
		parsed := ParseID(id)
		// 重新组合ID
		reconstructedID := (parsed.Sequence << maskBits) | parsed.Mask
		if reconstructedID == id {
			fmt.Printf("ID %d 验证通过\n", i+1)
		} else {
			fmt.Printf("ID %d 验证失败: 原始=%d, 重构=%d\n", i+1, id, reconstructedID)
		}
	}

	// 展示ID的位结构
	fmt.Println("\nID位结构示例:")
	if len(ids) > 0 {
		id := ids[0]
		fmt.Printf("完整ID: %064b\n", id)

		parsed := ParseID(id)
		fmt.Printf("序列号部分(高32位): %032b (十进制: %d)\n", parsed.Sequence, parsed.Sequence)
		fmt.Printf("掩码部分(低8位):     %08b (十进制: %d)\n", parsed.Mask, parsed.Mask)
	}

	fmt.Println("\n测试完成")
}

// ParseID 解析一个由 IDGenerator 生成的ID
func ParseID(id int64) ParsedIDInfo {
	// 1. 提取随机掩码 (Mask)
	// 掩码在ID的最低8位，我们用`&`运算和一个8位的掩码来提取它。
	mask := id & maxMask

	// 2. 提取序列号 (Sequence)
	// 把ID右移8位，让序列号部分移动到最右边，然后再用32位的掩码提取。
	sequence := (id >> maskBits) & maxSequence

	return ParsedIDInfo{
		Sequence: sequence,
		Mask:     mask,
	}
}

type ParsedIDInfo struct {
	Sequence int64 // 序列号
	Mask     int64 // 随机掩码
}

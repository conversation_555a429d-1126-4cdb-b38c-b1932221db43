package logic

import (
	"context"
	"errors"
	"loginsrv/internal/repo/rpc_user"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/applestore"
	"github.com/sirupsen/logrus"
)

// AppleLogin Apple登录实现
type AppleLogin struct {
	*BaseLogin
}

// NewAppleLogin 创建Apple登录实例
func NewAppleLogin() *AppleLogin {
	return &AppleLogin{
		BaseLogin: NewBaseLogin(commonPB.LOGIN_TYPE_LT_APPLE),
	}
}

// validateRequest 验证请求参数
func (l *AppleLogin) validateRequest(req *loginRpc.LoginReq) error {
	if err := l.ValidateBaseParams(req); err != nil {
		return err
	}

	if req.GetThirdInfo() == nil || req.GetThirdInfo().GetCodeId() == "" {
		return errors.New("third info or code is empty")
	}

	return nil
}

// Login Apple登录流程
func (l *AppleLogin) Login(ctx context.Context, req *loginRpc.LoginReq) (*loginRpc.LoginRsp, error) {
	entry := logx.NewLogEntry(ctx)
	rsp := &loginRpc.LoginRsp{Ret: protox.DefaultResult()}
	rsp.Ret.Desc += "apple login failed"

	// 验证请求参数
	if err := l.validateRequest(req); err != nil {
		entry.Error(err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM, err.Error())
		return rsp, err
	}

	a := applestore.CredsRegistry.Get(req.GetChannelId())
	logrus.Infof("Apple login: %+v", *a)

	// 调用 SDK 验证 Apple token
	userInfo, err := sdk.GetUserInfo(&sdk.LoginParams{
		ChannelID:   req.GetChannelId(),
		AccType:     l.LoginType,
		AccessToken: req.GetThirdInfo().GetToken(),
		Code:        req.GetThirdInfo().GetCodeId(),
	})
	if err != nil {
		entry.Errorf("Apple login: verify token failed: %v", err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO, err.Error())
		return rsp, err
	}
	if userInfo == nil {
		err = errors.New("user info is nil")
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO, err.Error())
		return rsp, err
	}
	if req.GetThirdInfo().GetOpenId() != userInfo.OpenID {
		err = errors.New("open id not match")
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO, err.Error())
		return rsp, err
	}

	// 获取玩家ID
	playerId, err := rpc_user.GetPlayerIdByOpenId(ctx, req.GetProductId(), req.GetLoginType(), req.GetThirdInfo().GetOpenId())
	if err != nil {
		entry.Errorf("get player id failed: %v", err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, err.Error())
		return rsp, err
	}

	// 如果玩家ID为0，则表示玩家不存在，需要注册
	if playerId == 0 {
		req.IsReg = true
	}

	// 处理登录流程
	return l.DefaultLoginProcess(ctx, req, playerId)
}

package version_check

import (
	"context"
	whiteList "loginsrv/internal/logic/white_list"

	"git.keepfancy.xyz/back-end/frameworks/kit/version"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
)

// VersionCheck 版本更新判断 针对客户端是否强更检查
func VersionCheck(ctx context.Context, playerId uint64, req *loginRpc.LoginReq) commonPB.ErrCode {
	if req == nil {
		return commonPB.ErrCode_ERR_BAD_PARAM
	}
	entry := logx.NewLogEntry(ctx)

	// 强更版本校验
	appUpdConf := cmodel.GetAppUpdateInfo(consul_config.WithChannel(int32(req.GetChannelId())), consul_config.WithProduct(int(req.GetProductId())))
	if appUpdConf == nil || !appUpdConf.IsForce {
		return commonPB.ErrCode_ERR_SUCCESS
	}

	// 登录白名单 可登录
	if whiteList.IsInLoginWhiteList(ctx, playerId, req) {
		entry.Debugf("playerId:%+v in login white list", playerId)
		return commonPB.ErrCode_ERR_SUCCESS
	}

	// 先判断版本
	if version.Compare(req.GetClientVersion(), appUpdConf.MinAppVersion) < 0 {
		return commonPB.ErrCode_ERR_LOGIN_VERSION_TOO_LOW
	}

	// 判断是否是维护时间
	if appUpdConf.DowntimeStart > 0 && appUpdConf.DowntimeEnd > 0 {
		nowTime := timex.Now().Unix()
		if nowTime >= appUpdConf.DowntimeStart && nowTime <= appUpdConf.DowntimeEnd {
			return commonPB.ErrCode_ERR_LOGIN_DOWNTIME_STATUS
		}
	}

	return commonPB.ErrCode_ERR_SUCCESS
}

package logic_abtest

import (
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/testx"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

func loadConfig() {
	viper.SetConfigFile("../../../cmd/config.yml")
	err := viper.ReadInConfig()
	if err != nil {
		logrus.Errorf("loadConfig error: %v", err)
	}
	keys := viper.AllKeys()
	logrus.Infof("keys: %v", keys)
}

func TestLoad(t *testing.T) {
	testx.Init()
	
	logrus.SetLevel(logrus.DebugLevel)
	loadConfig()
	err := Init()
	if err != nil {
		t.Fatalf("Init error: %v", err)
	}
	t.Logf("Init success")
}

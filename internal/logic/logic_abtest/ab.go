package logic_abtest

import (
	"errors"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"github.com/spf13/viper"

	// 用于读取init配置
	_ "git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
)

var (
	abConfig *ABConfig
)

type ABConfig struct {
	Channel     int32
	TestChannel int32
}

func (data *ABConfig) String() string {
	return fmt.Sprintf("Channel: %d, TestChannel: %d", data.Channel, data.TestChannel)
}

// 初始化加载
func Init() error {
	abConfig = &ABConfig{}
	conf := viper.Get("ab_config_channel")
	logrus.Debugf("ab_config_channel: %+v", conf)
	// 分解第二层数据
	mapData := conf.(map[string]any)
	config := &ABConfig{
		Channel:     cast.ToInt32(mapData["atest"]),
		TestChannel: cast.ToInt32(mapData["btest"]),
	}
	abConfig = config

	logrus.Debugf("abConfig: %+v", abConfig)

	// 校验配置有效性
	channels := consul_config.GetInstance().GetAllChannel(consul_config.WithProduct(int(commonPB.PRODUCT_ID_PID_FISHER)))
	flag := false

	for _, channel := range channels {
		if channel == abConfig.TestChannel {
			flag = true
			break
		}
	}
	if !flag {
		return errors.New("test channel not exist")
	}
	return nil
}

// 染色标签
func DyeLabel(playerId uint64, channel commonPB.CHANNEL_TYPE) int32 {
	newChannel := int32(channel)
	// 染色以尾号区分
	if playerId%2 == 0 {
		// 分配到实验组
		if newChannel == abConfig.Channel {
			newChannel = abConfig.TestChannel
		}
	}

	return newChannel
}

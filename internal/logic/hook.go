package logic

import (
	"context"
	"fmt"
	logicAnti "loginsrv/internal/logic/anti_addiction"
	"loginsrv/internal/logic/block"
	"loginsrv/internal/logic/logic_abtest"

	game_status "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/game_status"

	versionCheck "loginsrv/internal/logic/version_check"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
)

// DefaultLoginHooks 默认的钩子空实现
type DefaultLoginHooks struct{}

func (h *DefaultLoginHooks) BeforeLogin(ctx context.Context, playerId uint64, req *loginRpc.LoginReq) (commonPB.ErrCode, error) {
	if req.GetDeviceInfo() == nil {
		return commonPB.ErrCode_ERR_BAD_PARAM, fmt.Errorf("client ip is empty")
	}

	// 版本更新检查
	errCode := versionCheck.VersionCheck(ctx, playerId, req)
	if errCode != commonPB.ErrCode_ERR_SUCCESS {
		return errCode, fmt.Errorf("version update check failed: %v", errCode)
	}

	// 限制登录检查
	if block.CheckLoginBlock(ctx, playerId, req) {
		return commonPB.ErrCode_ERR_LOGIN_BLOCK_LOCATION, fmt.Errorf("login block playerId=%d ip=%s", playerId, req.GetDeviceInfo().GetClientIp())
	}

	return commonPB.ErrCode_ERR_SUCCESS, nil
}

func (h *DefaultLoginHooks) AfterLogin(ctx context.Context, req *loginRpc.LoginReq, rsp *loginRpc.LoginRsp) (commonPB.ErrCode, error) {
	// 检查实名认证和防沉迷
	if err := logicAnti.CheckRealNameAndAntiAddiction(ctx, req, rsp); err != nil {
		return commonPB.ErrCode_ERR_LOGIN_ANTI_ADDICTION, err
	}

	// 玩家灰度标签
	grayTag := game_status.CalculatePlayerGrayStatus(ctx, rsp.GetPlayerId(), req.GetDeviceInfo().GetClientIp(), req.GetClientVersion())
	if grayTag > 0 {
		rsp.GrayStrategy = commonPB.GRAY_STRATEGY(grayTag)
	}

	dyeLabel := logic_abtest.DyeLabel(rsp.GetPlayerId(), req.GetChannelId())
	if dyeLabel > 0 {
		rsp.DyedLabel = dyeLabel
	}

	return commonPB.ErrCode_ERR_SUCCESS, nil
}

// LoginHooksRegistry 登录钩子注册表
type LoginHooksRegistry struct {
	hooks map[commonPB.LOGIN_TYPE]LoginHooks
}

// DefaultLoginHooksRegistry 默认的登录钩子注册表实例
var DefaultLoginHooksRegistry = NewLoginHooksRegistry()

// NewLoginHooksRegistry 创建登录钩子注册表
func NewLoginHooksRegistry() *LoginHooksRegistry {
	return &LoginHooksRegistry{
		hooks: make(map[commonPB.LOGIN_TYPE]LoginHooks),
	}
}

// RegisterHooks 注册特定登录类型的钩子
func (r *LoginHooksRegistry) RegisterHooks(loginType commonPB.LOGIN_TYPE, hooks LoginHooks) {
	if hooks != nil {
		r.hooks[loginType] = hooks
	}
}

// GetHooks 获取指定登录类型的钩子
func (r *LoginHooksRegistry) GetHooks(loginType commonPB.LOGIN_TYPE) LoginHooks {
	if hooks, ok := r.hooks[loginType]; ok {
		return hooks
	}
	return &DefaultLoginHooks{}
}

package logic

import (
	"context"
	"errors"
	"loginsrv/internal/repo/rpc_user"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk"
)

// GoogleLogin Google登录实现
type GoogleLogin struct {
	*BaseLogin
}

// NewGoogleLogin 创建Google登录实例
func NewGoogleLogin() *GoogleLogin {
	return &GoogleLogin{
		BaseLogin: NewBaseLogin(commonPB.LOGIN_TYPE_LT_GOOGLE),
	}
}

// validateRequest 验证请求参数
func (l *GoogleLogin) validateRequest(req *loginRpc.LoginReq) error {
	if err := l.ValidateBaseParams(req); err != nil {
		return err
	}

	if req.GetThirdInfo() == nil || req.GetThirdInfo().GetCodeId() == "" {
		return errors.New("third info or code is empty")
	}

	return nil
}

// Login Google登录流程
func (l *GoogleLogin) Login(ctx context.Context, req *loginRpc.LoginReq) (*loginRpc.LoginRsp, error) {
	entry := logx.NewLogEntry(ctx)
	rsp := &loginRpc.LoginRsp{Ret: protox.DefaultResult()}
	rsp.Ret.Desc += "google login failed"

	// 验证请求参数
	if err := l.validateRequest(req); err != nil {
		entry.Error(err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM, err.Error())
		return rsp, err
	}

	// 调用 SDK 验证 Google token
	userInfo, err := sdk.GetUserInfo(&sdk.LoginParams{
		ChannelID: req.GetChannelId(),
		AccType:   l.LoginType,
		Code:      req.GetThirdInfo().GetCodeId(),
	})
	if err != nil {
		entry.Errorf("Google login: verify token failed: %v", err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO, err.Error())
		return rsp, err
	}
	if userInfo == nil {
		err = errors.New("user info is nil")
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO, err.Error())
		return rsp, err
	}
	if req.GetThirdInfo().GetOpenId() != userInfo.OpenID {
		err = errors.New("open id not match")
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO, err.Error())
		return rsp, err
	}

	// 获取玩家ID
	playerId, err := rpc_user.GetPlayerIdByOpenId(ctx, req.GetProductId(), req.GetLoginType(), req.GetThirdInfo().GetOpenId())
	if err != nil {
		entry.Errorf("get player id failed: %v", err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, err.Error())
		return rsp, err
	}

	// 如果玩家ID为0，则表示玩家不存在，需要注册
	if playerId == 0 {
		req.IsReg = true
	}

	// 处理登录流程
	return l.DefaultLoginProcess(ctx, req, playerId)
}

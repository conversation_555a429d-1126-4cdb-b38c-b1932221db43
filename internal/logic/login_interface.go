package logic

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
)

// ILogin 登录接口
type ILogin interface {
	Login(ctx context.Context, req *loginRpc.LoginReq) (*loginRpc.LoginRsp, error)
	SetHooks(hooks LoginHooks)
}

// LoginHooks 登录钩子接口
// 可在登录前后添加自定义逻辑
type LoginHooks interface {
	// BeforeLogin 登录前执行的钩子
	BeforeLogin(ctx context.Context, playerId uint64, req *loginRpc.LoginReq) (commonPB.ErrCode, error)
	// AfterLogin 登录后执行的钩子
	AfterLogin(ctx context.Context, req *loginRpc.LoginReq, rsp *loginRpc.LoginRsp) (commonPB.ErrCode, error)
}

// NewLoginImpl 创建登录实现实例
func NewLoginImpl(loginType commonPB.LOGIN_TYPE) ILogin {
	// 从钩子注册表中获取对应登录类型的钩子
	hooks := DefaultLoginHooksRegistry.GetHooks(loginType)

	var login ILogin
	switch loginType {
	case commonPB.LOGIN_TYPE_LT_VISITOR:
		login = NewVisitorLogin()
	case commonPB.LOGIN_TYPE_LT_TOKEN:
		login = NewTokenLogin()
	case commonPB.LOGIN_TYPE_LT_PASSWORD:
		login = NewAccountLogin()
	case commonPB.LOGIN_TYPE_LT_GOOGLE:
		login = NewGoogleLogin()
	case commonPB.LOGIN_TYPE_LT_FACEBOOK:
		login = NewFacebookLogin()
	case commonPB.LOGIN_TYPE_LT_APPLE:
		login = NewAppleLogin()
	default:
		return nil
	}

	// 设置钩子
	if login != nil {
		login.SetHooks(hooks)
	}

	return login
}

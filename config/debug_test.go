package config

import (
	"testing"

	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
)

func TestSDKCredentialsUnmarshalWithJsonTag(t *testing.T) {
	// 设置测试配置
	viper.Set("sdk_credentials", map[string]interface{}{
		"1001": map[string]interface{}{
			"apple": map[string]interface{}{
				"pkg_name":  "com.playsparkle.apps.mt.ios",
				"client_id": "1626967967",
				"key_id":    "6U85ZUQ8TD",
				"team_id":   "X2T26K95ZU",
				"secret":    "test-secret",
			},
			"facebook": map[string]interface{}{
				"client_secret": "f3205917fb373d49810673b218e4d859",
			},
			"google": map[string]interface{}{
				"client_id":     "1042706838390-2tdjhdhrcall2rieio1nchfi1esg4epg.apps.googleusercontent.com",
				"client_secret": "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",
			},
		},
	})

	config := &mapstructure.DecoderConfig{
		TagName: "json",
		Result:  &SDKCredentialsConfig{},
	}
	decoder, err := mapstructure.NewDecoder(config)
	assert.NoError(t, err)

	// 获取原始配置数据
	rawConfig := viper.Get("sdk_credentials")
	assert.NotNil(t, rawConfig)

	// 解析配置
	var sdkConfig SDKCredentialsConfig
	err = decoder.Decode(rawConfig)
	assert.NoError(t, err)

	// 检查解析结果
	assert.NotNil(t, sdkConfig["1001"])
	
	if sdkConfig["1001"].Apple != nil {
		t.Logf("Apple config: %+v", sdkConfig["1001"].Apple)
		// 检查具体字段
		apple := sdkConfig["1001"].Apple
		t.Logf("PkgName: %s", apple.PkgName)
		t.Logf("ClientID: %s", apple.ClientID)
		t.Logf("KeyID: %s", apple.KeyID)
		t.Logf("TeamID: %s", apple.TeamID)
		t.Logf("Secret: %s", apple.Secret)
		
		// 验证字段是否正确解析
		assert.Equal(t, "com.playsparkle.apps.mt.ios", apple.PkgName)
		assert.Equal(t, "1626967967", apple.ClientID)
		assert.Equal(t, "6U85ZUQ8TD", apple.KeyID)
		assert.Equal(t, "X2T26K95ZU", apple.TeamID)
		assert.Equal(t, "test-secret", apple.Secret)
	} else {
		t.Log("Apple config is nil")
	}

	if sdkConfig["1001"].Facebook != nil {
		t.Logf("Facebook config: %+v", sdkConfig["1001"].Facebook)
		assert.Equal(t, "f3205917fb373d49810673b218e4d859", sdkConfig["1001"].Facebook.ClientSecret)
	} else {
		t.Log("Facebook config is nil")
	}

	if sdkConfig["1001"].Google != nil {
		t.Logf("Google config: %+v", sdkConfig["1001"].Google)
		assert.Equal(t, "1042706838390-2tdjhdhrcall2rieio1nchfi1esg4epg.apps.googleusercontent.com", sdkConfig["1001"].Google.ClientId)
		assert.Equal(t, "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", sdkConfig["1001"].Google.ClientSecret)
	} else {
		t.Log("Google config is nil")
	}
}

func TestInitSDKCredentials(t *testing.T) {
	// 设置测试配置
	viper.Set("sdk_credentials", map[string]interface{}{
		"1001": map[string]interface{}{
			"apple": map[string]interface{}{
				"pkg_name":  "com.playsparkle.apps.mt.ios",
				"client_id": "1626967967",
				"key_id":    "6U85ZUQ8TD",
				"team_id":   "X2T26K95ZU",
				"secret":    "test-secret",
			},
		},
	})

	// 测试初始化函数
	err := InitSDKCredentials()
	assert.NoError(t, err)
}

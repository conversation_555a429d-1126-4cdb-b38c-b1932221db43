package config

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/applestore"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/facebook"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/google"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/mitchellh/mapstructure"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// SDKCredentialsConfig 完整的SDK凭证配置 - 按渠道ID组织
type SDKCredentialsConfig map[string]*sdk.Credentials

// InitSDKCredentials 初始化SDK凭证配置
func InitSDKCredentials() error {
	entry := logx.NewLogEntry(context.Background())
	entry.Debugf("Initializing SDK credentials from config...")

	// 配置mapstructure使用json标签
	config := &mapstructure.DecoderConfig{
		TagName: "json",
		Result:  &SDKCredentialsConfig{},
	}
	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		entry.Errorf("Failed to create decoder: %v", err)
		return err
	}

	// 获取原始配置数据
	rawConfig := viper.Get("sdk_credentials")
	if rawConfig == nil {
		entry.Warn("No sdk_credentials found in config")
		return nil
	}

	// 解析sdk_credentials配置，按渠道ID组织
	var sdkConfig SDKCredentialsConfig
	if err = decoder.Decode(&sdkConfig); err != nil {
		entry.Errorf("Failed to decode SDK credentials config: %v", err)
		return err
	}

	logrus.Infof("SDK credentials config: %+v", sdkConfig)

	// 详细打印每个渠道的配置
	for channelID, config := range sdkConfig {
		entry.Infof("Channel %s config: %+v", channelID, config)
		if config != nil {
			if config.Apple != nil {
				entry.Infof("Channel %s Apple: %+v", channelID, config.Apple)
			}
			if config.Facebook != nil {
				entry.Infof("Channel %s Facebook: %+v", channelID, config.Facebook)
			}
			if config.Google != nil {
				entry.Infof("Channel %s Google: %+v", channelID, config.Google)
			}
		}
	}

	// 遍历所有渠道配置
	for channelIDStr, channelConfig := range sdkConfig {
		if channelConfig == nil {
			continue
		}

		// 将字符串渠道ID转换为整数
		channelID := commonPB.CHANNEL_TYPE(transform.ToInt64(channelIDStr))
		if channelID == 0 {
			logrus.Warnf("Invalid channel ID: %s", channelIDStr)
			continue
		}

		// 设置渠道凭证
		setChannelCredentials(channelID, channelConfig)
	}

	entry.Debugf("SDK credentials initialization completed")
	return nil
}

// setChannelCredentials 为指定渠道设置凭证
func setChannelCredentials(channelType commonPB.CHANNEL_TYPE, config *sdk.Credentials) {
	// Apple凭证
	if config.Apple != nil {
		applestore.SetCredentials(channelType, config.Apple)
	}

	// Facebook凭证
	if config.Facebook != nil {
		facebook.SetCredentials(channelType, config.Facebook)
	}

	// Google凭证
	if config.Google != nil {
		google.SetCredentials(channelType, config.Google)
	}

	logrus.Debugf("SDK credentials loaded for channel: %d", channelType)
}

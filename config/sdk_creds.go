package config

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/spf13/viper"
)

// SDKCredentialsConfig 完整的SDK凭证配置 - 按渠道ID组织
type SDKCredentialsConfig map[string]*sdk.Credentials

// InitSDKCredentials 初始化SDK凭证配置
func InitSDKCredentials() error {
	entry := logx.NewLogEntry(context.Background())
	entry.Debugf("Initializing SDK credentials from config...")

	// 解析sdk_credentials配置
	var sdkConfig SDKCredentialsConfig
	if err := viper.UnmarshalKey("sdk_credentials", &sdkConfig); err != nil {
		entry.Errorf("Failed to unmarshal SDK credentials config: %v", err)
		return err
	}

	// 遍历所有渠道配置
	for channelIDStr, channelConfig := range sdkConfig {
		if channelConfig == nil {
			continue
		}
		channelID := commonPB.CHANNEL_TYPE(transform.ToInt64(channelIDStr))
		if channelID == 0 {
			entry.Errorf("Invalid channel ID: %s", channelIDStr)
			continue
		}
		// 设置渠道凭证
		sdk.SetCredentials(channelID, channelConfig)
	}

	entry.Debugf("SDK credentials initialization completed")
	return nil
}

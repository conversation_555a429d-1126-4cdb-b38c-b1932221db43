package config

import (
	"strings"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
)

func TestYAMLLiteralScalar(t *testing.T) {
	// 测试YAML字面量标量（|）的解析
	yamlContent := `
sdk_credentials:
  1001:
    apple:
      pkg_name: "com.qh.fishinggame"
      client_id: "1626967967"
      key_id: "QHY6ACDD6W"
      team_id: "6789BM3S46"
      secret: |
        -----BEGIN PRIVATE KEY-----
        MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgXZKlnAfAqUAEoACW
        ppQkjEbU26kXQ6qjrKmKr/8lUSagCgYIKoZIzj0DAQehRANCAATSEr+o/anXCRus
        EtlzQNEg5z/u5a6dUwRccf2WIUTN+X7I4K/N/SAu/Xj8/P2ZGg0xKmn/i6EQ/Ruc
        SJasgyAf
        -----E<PERSON> PRIVATE KEY-----
    facebook:
      client_secret: "f3205917fb373d49810673b218e4d859"
    google:
      client_id: "1042706838390-2tdjhdhrcall2rieio1nchfi1esg4epg.apps.googleusercontent.com"
      client_secret: "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"
`

	// 创建一个新的viper实例
	v := viper.New()
	v.SetConfigType("yaml")
	
	// 读取YAML内容
	err := v.ReadConfig(strings.NewReader(yamlContent))
	assert.NoError(t, err)

	// 解析SDK凭证配置
	var sdkConfig SDKCredentialsConfig
	err = v.UnmarshalKey("sdk_credentials", &sdkConfig)
	assert.NoError(t, err)

	// 验证解析结果
	assert.NotNil(t, sdkConfig["1001"])
	assert.NotNil(t, sdkConfig["1001"].Apple)

	apple := sdkConfig["1001"].Apple
	assert.Equal(t, "com.qh.fishinggame", apple.PkgName)
	assert.Equal(t, "1626967967", apple.ClientID)
	assert.Equal(t, "QHY6ACDD6W", apple.KeyID)
	assert.Equal(t, "6789BM3S46", apple.TeamID)

	// 验证secret字段包含完整的私钥
	assert.Contains(t, apple.Secret, "-----BEGIN PRIVATE KEY-----")
	assert.Contains(t, apple.Secret, "-----END PRIVATE KEY-----")
	assert.Contains(t, apple.Secret, "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQg")

	t.Logf("Secret length: %d", len(apple.Secret))
	t.Logf("Secret content:\n%s", apple.Secret)

	// 验证Facebook配置
	assert.NotNil(t, sdkConfig["1001"].Facebook)
	assert.Equal(t, "f3205917fb373d49810673b218e4d859", sdkConfig["1001"].Facebook.ClientSecret)

	// 验证Google配置
	assert.NotNil(t, sdkConfig["1001"].Google)
	assert.Equal(t, "1042706838390-2tdjhdhrcall2rieio1nchfi1esg4epg.apps.googleusercontent.com", sdkConfig["1001"].Google.ClientId)
	assert.Equal(t, "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", sdkConfig["1001"].Google.ClientSecret)
}

func TestRealConfigFile(t *testing.T) {
	// 测试实际的config.yml文件
	v := viper.New()
	v.SetConfigName("config")
	v.SetConfigType("yaml")
	v.AddConfigPath("../cmd")
	
	err := v.ReadInConfig()
	if err != nil {
		t.Skipf("Could not read config file: %v", err)
		return
	}

	// 解析SDK凭证配置
	var sdkConfig SDKCredentialsConfig
	err = v.UnmarshalKey("sdk_credentials", &sdkConfig)
	assert.NoError(t, err)

	t.Logf("Parsed SDK credentials: %+v", sdkConfig)

	// 检查1001渠道的Apple配置
	if sdkConfig["1001"] != nil && sdkConfig["1001"].Apple != nil {
		apple := sdkConfig["1001"].Apple
		t.Logf("Apple config for channel 1001:")
		t.Logf("  PkgName: %s", apple.PkgName)
		t.Logf("  ClientID: %s", apple.ClientID)
		t.Logf("  KeyID: %s", apple.KeyID)
		t.Logf("  TeamID: %s", apple.TeamID)
		t.Logf("  Secret length: %d", len(apple.Secret))
		
		// 验证所有字段都不为空
		assert.NotEmpty(t, apple.PkgName)
		assert.NotEmpty(t, apple.ClientID)
		assert.NotEmpty(t, apple.KeyID)
		assert.NotEmpty(t, apple.TeamID)
		assert.NotEmpty(t, apple.Secret)
		
		// 验证secret包含私钥内容
		assert.Contains(t, apple.Secret, "-----BEGIN PRIVATE KEY-----")
		assert.Contains(t, apple.Secret, "-----END PRIVATE KEY-----")
	}
}

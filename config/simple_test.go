package config

import (
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
)

func TestInitSDKCredentialsSimple(t *testing.T) {
	// 设置测试配置，使用实际的config.yml格式
	viper.Set("sdk_credentials", map[string]interface{}{
		"1001": map[string]interface{}{
			"apple": map[string]interface{}{
				"pkg_name":  "com.playsparkle.apps.mt.ios",
				"client_id": "1626967967",
				"key_id":    "6U85ZUQ8TD",
				"team_id":   "X2T26K95ZU",
				"secret":    "test-secret",
			},
			"facebook": map[string]interface{}{
				"client_secret": "f3205917fb373d49810673b218e4d859",
			},
			"google": map[string]interface{}{
				"client_id":     "1042706838390-2tdjhdhrcall2rieio1nchfi1esg4epg.apps.googleusercontent.com",
				"client_secret": "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",
			},
		},
		"1002": map[string]interface{}{
			"facebook": map[string]interface{}{
				"client_secret": "f3205917fb373d49810673b218e4d859",
			},
		},
	})

	// 测试初始化函数
	err := InitSDKCredentials()
	assert.NoError(t, err)
	
	t.Log("SDK credentials initialization completed successfully")
}

func TestRawConfigAccess(t *testing.T) {
	// 设置测试配置
	viper.Set("sdk_credentials", map[string]interface{}{
		"1001": map[string]interface{}{
			"apple": map[string]interface{}{
				"pkg_name":  "com.playsparkle.apps.mt.ios",
				"client_id": "1626967967",
				"key_id":    "6U85ZUQ8TD",
				"team_id":   "X2T26K95ZU",
				"secret":    "test-secret",
			},
		},
	})

	// 获取原始配置数据
	rawConfig := viper.Get("sdk_credentials")
	assert.NotNil(t, rawConfig)
	
	t.Logf("Raw config: %+v", rawConfig)
	
	// 检查嵌套结构
	if configMap, ok := rawConfig.(map[string]interface{}); ok {
		if channel1001, exists := configMap["1001"]; exists {
			t.Logf("Channel 1001: %+v", channel1001)
			
			if channelMap, ok := channel1001.(map[string]interface{}); ok {
				if apple, exists := channelMap["apple"]; exists {
					t.Logf("Apple config: %+v", apple)
					
					if appleMap, ok := apple.(map[string]interface{}); ok {
						t.Logf("pkg_name: %v", appleMap["pkg_name"])
						t.Logf("client_id: %v", appleMap["client_id"])
						t.Logf("key_id: %v", appleMap["key_id"])
						t.Logf("team_id: %v", appleMap["team_id"])
						t.Logf("secret: %v", appleMap["secret"])
					}
				}
			}
		}
	}
}
